import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { Room, Message, ChatState } from '../types'
import { roomsApi, messagesApi, ApiError } from '../utils/api'
import socketManager from '../utils/socket'
import { useAuth } from '../hooks/useAuth'

interface ChatContextType extends ChatState {
  joinRoom: (roomId: string) => Promise<void>
  leaveRoom: (roomId: string) => Promise<void>
  sendMessage: (content: string, roomId: string) => void
  createRoom: (name: string, description?: string, isPrivate?: boolean) => Promise<any>
  loadRooms: () => Promise<void>
  loadMessages: (roomId: string) => Promise<void>
  loadRoomDetails: (roomId: string) => Promise<void>
  setCurrentRoom: (room: Room | null) => void
  startTyping: (roomId: string) => void
  stopTyping: (roomId: string) => void
  error: string | null
  clearError: () => void
}

type ChatAction =
  | { type: 'SET_ROOMS'; payload: Room[] }
  | { type: 'SET_CURRENT_ROOM'; payload: Room | null }
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_ONLINE_USERS'; payload: { roomId: string; users: { id: string; username: string }[] } }
  | { type: 'SET_TYPING_USERS'; payload: { roomId: string; users: { userId: string; username: string }[] } }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' }

const initialState: ChatState & { error: string | null } = {
  rooms: [],
  currentRoom: null,
  messages: [],
  onlineUsers: {},
  typingUsers: {},
  isConnected: false,
  error: null
}

function chatReducer(
  state: ChatState & { error: string | null },
  action: ChatAction
): ChatState & { error: string | null } {
  switch (action.type) {
    case 'SET_ROOMS':
      return { ...state, rooms: action.payload }
    case 'SET_CURRENT_ROOM':
      return { ...state, currentRoom: action.payload }
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload }
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] }
    case 'SET_ONLINE_USERS':
      return {
        ...state,
        onlineUsers: {
          ...state.onlineUsers,
          [action.payload.roomId]: action.payload.users
        }
      }
    case 'SET_TYPING_USERS':
      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [action.payload.roomId]: action.payload.users
        }
      }
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    case 'CLEAR_ERROR':
      return { ...state, error: null }
    default:
      return state
  }
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function ChatProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(chatReducer, initialState)
  const { isAuthenticated, token } = useAuth()

  // Initialize socket connection when authenticated
  useEffect(() => {
    if (isAuthenticated && token) {
      const socket = socketManager.connect(token)

      // Socket event listeners
      socketManager.onConnect(() => {
        dispatch({ type: 'SET_CONNECTED', payload: true })
      })

      socketManager.onDisconnect(() => {
        dispatch({ type: 'SET_CONNECTED', payload: false })
      })

      socketManager.onNewMessage((message: Message) => {
        dispatch({ type: 'ADD_MESSAGE', payload: message })
      })

      socketManager.onOnlineUsersUpdate((data: { roomId: string; onlineUsers: { id: string; username: string }[] }) => {
        dispatch({
          type: 'SET_ONLINE_USERS',
          payload: { roomId: data.roomId, users: data.onlineUsers }
        })
      })

      socketManager.onUserTyping((data: { userId: string; username: string; roomId: string; isTyping: boolean }) => {
        const currentTypingUsers = state.typingUsers[data.roomId] || []
        let updatedTypingUsers

        if (data.isTyping) {
          // Add user if not already typing
          const userAlreadyTyping = currentTypingUsers.some(user => user.userId === data.userId)
          if (!userAlreadyTyping) {
            updatedTypingUsers = [...currentTypingUsers, { userId: data.userId, username: data.username }]
          } else {
            updatedTypingUsers = currentTypingUsers
          }
        } else {
          // Remove user from typing list
          updatedTypingUsers = currentTypingUsers.filter(user => user.userId !== data.userId)
        }

        dispatch({
          type: 'SET_TYPING_USERS',
          payload: { roomId: data.roomId, users: updatedTypingUsers }
        })
      })

      socketManager.onError((data: { message: string }) => {
        dispatch({ type: 'SET_ERROR', payload: data.message })
      })

      return () => {
        socketManager.disconnect()
      }
    }
  }, [isAuthenticated, token])

  const loadRooms = async () => {
    try {
      const response = await roomsApi.getRooms()
      dispatch({ type: 'SET_ROOMS', payload: response.rooms })
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Failed to load rooms'
      dispatch({ type: 'SET_ERROR', payload: message })
    }
  }

  const loadMessages = async (roomId: string) => {
    try {
      const response = await messagesApi.getRecentMessages(roomId)
      dispatch({ type: 'SET_MESSAGES', payload: response.messages })
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Failed to load messages'
      dispatch({ type: 'SET_ERROR', payload: message })
    }
  }

  const joinRoom = async (roomId: string) => {
    try {
      // Try to join the room via API
      await roomsApi.joinRoom(roomId)
    } catch (error) {
      // If already a member, that's fine - we can still enter the room
      if (error instanceof ApiError && error.message === 'Already a member of this room') {
        // Continue with socket join and loading messages
      } else {
        const message = error instanceof ApiError ? error.message : 'Failed to join room'
        dispatch({ type: 'SET_ERROR', payload: message })
        throw error
      }
    }
    
    try {
      // Join the socket room and load messages and room details
      socketManager.joinRoom(roomId)
      await Promise.all([
        loadMessages(roomId),
        loadRoomDetails(roomId)
      ])
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Failed to connect to room'
      dispatch({ type: 'SET_ERROR', payload: message })
      throw error
    }
  }

  const leaveRoom = async (roomId: string) => {
    try {
      await roomsApi.leaveRoom(roomId)
      socketManager.leaveRoom(roomId)
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Failed to leave room'
      dispatch({ type: 'SET_ERROR', payload: message })
      throw error
    }
  }

  const sendMessage = (content: string, roomId: string) => {
    socketManager.sendMessage(content, roomId)
  }

  const createRoom = async (name: string, description?: string, isPrivate?: boolean) => {
    try {
      const response = await roomsApi.createRoom({ name, description, isPrivate })
      await loadRooms() // Refresh rooms list
      return response
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Failed to create room'
      dispatch({ type: 'SET_ERROR', payload: message })
      throw error
    }
  }

  const loadRoomDetails = async (roomId: string) => {
    try {
      const response = await roomsApi.getRoom(roomId)
      dispatch({ type: 'SET_CURRENT_ROOM', payload: response.room })
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Failed to load room details'
      dispatch({ type: 'SET_ERROR', payload: message })
    }
  }

  const setCurrentRoom = (room: Room | null) => {
    dispatch({ type: 'SET_CURRENT_ROOM', payload: room })
  }

  const startTyping = (roomId: string) => {
    socketManager.startTyping(roomId)
  }

  const stopTyping = (roomId: string) => {
    socketManager.stopTyping(roomId)
  }

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  const value: ChatContextType = {
    ...state,
    joinRoom,
    leaveRoom,
    sendMessage,
    createRoom,
    loadRooms,
    loadMessages,
    loadRoomDetails,
    setCurrentRoom,
    startTyping,
    stopTyping,
    error: state.error,
    clearError
  }

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
}

export function useChat() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}
