export interface User {
  id: string;
  email: string;
  username: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Room {
  id: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  createdAt: string;
  updatedAt: string;
  members?: RoomMember[];
  _count?: {
    messages: number;
    members: number;
  };
}

export interface RoomMember {
  id: string;
  joinedAt: string;
  userId: string;
  roomId: string;
  user: {
    id: string;
    username: string;
  };
}

export interface Message {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  roomId: string;
  user: {
    id: string;
    username: string;
  };
}

export interface AuthResponse {
  message: string;
  user: User;
  token: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
}

export interface CreateRoomRequest {
  name: string;
  description?: string;
  isPrivate?: boolean;
}

export interface SendMessageRequest {
  content: string;
  roomId: string;
}

// Socket.IO event types
export interface SocketEvents {
  // Client to server
  authenticate: (token: string) => void;
  joinRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  sendMessage: (data: SendMessageRequest) => void;
  typingStart: (data: { roomId: string }) => void;
  typingStop: (data: { roomId: string }) => void;

  // Server to client
  authenticated: (data: { user: User }) => void;
  authenticationFailed: (data: { message: string }) => void;
  joinedRoom: (data: { roomId: string; roomName?: string }) => void;
  newMessage: (message: Message) => void;
  userJoined: (data: { user: { id: string; username: string }; room: { id: string; name?: string }; timestamp: string }) => void;
  userLeft: (data: { user: { id: string; username: string }; room: { id: string; name?: string }; timestamp: string }) => void;
  onlineUsersUpdate: (data: { roomId: string; onlineUsers: { id: string; username: string }[] }) => void;
  userTyping: (data: { userId: string; username: string; roomId: string; isTyping: boolean }) => void;
  error: (data: { message: string }) => void;
}

// Application state types
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface ChatState {
  rooms: Room[];
  currentRoom: Room | null;
  messages: Message[];
  onlineUsers: { [roomId: string]: { id: string; username: string }[] };
  typingUsers: { [roomId: string]: { userId: string; username: string }[] };
  isConnected: boolean;
}
