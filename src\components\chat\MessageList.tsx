import React, { useEffect, useRef } from 'react'
import { useChat } from '../../contexts/ChatContext'
import { useAuth } from '../../hooks/useAuth'
import { Message } from '../../types'

export default function MessageList() {
  const { messages, currentRoom, typingUsers } = useChat()
  const { user } = useAuth()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString()
    }
  }

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [date: string]: Message[] } = {}

    messages.forEach(message => {
      const date = new Date(message.createdAt).toDateString()
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(message)
    })

    return Object.entries(groups).sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
  }

  if (!currentRoom) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center text-gray-500">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
          <h3 className="text-lg font-medium mb-2">Welcome to Chat App</h3>
          <p>Select a room from the sidebar to start chatting</p>
        </div>
      </div>
    )
  }

  const messageGroups = groupMessagesByDate(messages)

  return (
    <div className="flex-1 overflow-y-auto bg-white">
      {/* Room Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 p-4 shadow-sm">
        <div className="flex items-center">
          <h2 className="text-xl font-semibold text-gray-900">#{currentRoom.name}</h2>
          {currentRoom.description && <span className="ml-3 text-gray-500">— {currentRoom.description}</span>}
        </div>
      </div>

      {/* Messages */}
      <div className="p-4 space-y-4">
        {messageGroups.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Be the first to say something!</p>
          </div>
        ) : (
          messageGroups.map(([date, dateMessages]) => (
            <div key={date}>
              {/* Date Separator */}
              <div className="flex items-center justify-center my-6">
                <div className="bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full">{formatDate(date)}</div>
              </div>

              {/* Messages for this date */}
              <div className="space-y-3">
                {dateMessages.map((message, index) => {
                  const isOwnMessage = message.user.id === user?.id
                  const showAvatar = index === 0 || dateMessages[index - 1].user.id !== message.user.id

                  return (
                    <div key={message.id} className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                      <div className={`flex max-w-xs lg:max-w-md ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                        {/* Avatar */}
                        <div className={`flex-shrink-0 ${isOwnMessage ? 'ml-2' : 'mr-2'}`}>
                          {showAvatar ? (
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                                isOwnMessage ? 'bg-indigo-600' : 'bg-gray-600'
                              }`}
                            >
                              {message.user.username.charAt(0).toUpperCase()}
                            </div>
                          ) : (
                            <div className="w-8 h-8"></div>
                          )}
                        </div>

                        {/* Message Content */}
                        <div className={`flex flex-col ${isOwnMessage ? 'items-end' : 'items-start'}`}>
                          {showAvatar && (
                            <div className={`text-sm text-gray-600 mb-1 ${isOwnMessage ? 'text-right' : 'text-left'}`}>
                              <span className="font-medium">{message.user.username}</span>
                              <span className="ml-2 text-gray-400">{formatTime(message.createdAt)}</span>
                            </div>
                          )}

                          <div
                            className={`px-4 py-2 rounded-lg ${
                              isOwnMessage ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-900'
                            }`}
                          >
                            <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
                          </div>

                          {!showAvatar && (
                            <div className="text-xs text-gray-400 mt-1">{formatTime(message.createdAt)}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          ))
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />

        {/* Typing Indicator */}
        {currentRoom && typingUsers[currentRoom.id] && typingUsers[currentRoom.id].length > 0 && (
          <div className="p-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
              <span>
                {typingUsers[currentRoom.id].length === 1 
                  ? `${typingUsers[currentRoom.id][0].username} is typing...`
                  : typingUsers[currentRoom.id].length === 2
                  ? `${typingUsers[currentRoom.id][0].username} and ${typingUsers[currentRoom.id][1].username} are typing...`
                  : `${typingUsers[currentRoom.id].length} people are typing...`
                }
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
