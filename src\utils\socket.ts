import { io, Socket } from 'socket.io-client';
import { SocketEvents } from '../types';

const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';

class SocketManager {
  private socket: Socket | null = null;
  private token: string | null = null;

  connect(token: string): Socket {
    if (this.socket?.connected) {
      this.socket.disconnect();
    }

    this.token = token;
    this.socket = io(SOCKET_URL, {
      autoConnect: false,
    });

    // Connect and authenticate
    this.socket.connect();
    this.socket.emit('authenticate', token);

    return this.socket;
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Convenience methods for common socket operations
  joinRoom(roomId: string): void {
    if (this.socket) {
      this.socket.emit('joinRoom', roomId);
    }
  }

  leaveRoom(roomId: string): void {
    if (this.socket) {
      this.socket.emit('leaveRoom', roomId);
    }
  }

  sendMessage(content: string, roomId: string): void {
    if (this.socket) {
      this.socket.emit('sendMessage', { content, roomId });
    }
  }

  startTyping(roomId: string): void {
    if (this.socket) {
      this.socket.emit('typingStart', { roomId });
    }
  }

  stopTyping(roomId: string): void {
    if (this.socket) {
      this.socket.emit('typingStop', { roomId });
    }
  }

  // Event listener helpers
  onAuthenticated(callback: (data: { user: any }) => void): void {
    this.socket?.on('authenticated', callback);
  }

  onAuthenticationFailed(callback: (data: { message: string }) => void): void {
    this.socket?.on('authenticationFailed', callback);
  }

  onNewMessage(callback: (message: any) => void): void {
    this.socket?.on('newMessage', callback);
  }

  onUserJoined(callback: (data: any) => void): void {
    this.socket?.on('userJoined', callback);
  }

  onUserLeft(callback: (data: any) => void): void {
    this.socket?.on('userLeft', callback);
  }

  onOnlineUsersUpdate(callback: (data: any) => void): void {
    this.socket?.on('onlineUsersUpdate', callback);
  }

  onJoinedRoom(callback: (data: { roomId: string; roomName?: string }) => void): void {
    this.socket?.on('joinedRoom', callback);
  }

  onError(callback: (data: { message: string }) => void): void {
    this.socket?.on('error', callback);
  }

  onConnect(callback: () => void): void {
    this.socket?.on('connect', callback);
  }

  onDisconnect(callback: () => void): void {
    this.socket?.on('disconnect', callback);
  }

  onUserTyping(callback: (data: { userId: string; username: string; roomId: string; isTyping: boolean }) => void): void {
    this.socket?.on('userTyping', callback);
  }

  // Remove event listeners
  off(event: string, callback?: (...args: any[]) => void): void {
    this.socket?.off(event, callback);
  }
}

// Export singleton instance
export const socketManager = new SocketManager();
export default socketManager;
